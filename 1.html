<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品智算引擎 v0.3 - 智能库存调拨</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        .hover-lift { transition: all 0.3s ease; }
        .hover-lift:hover { transform: translateY(-2px); box-shadow: 0 20px 40px -5px rgba(0, 0, 0, 0.15); }
        .pulse-animation { animation: pulse 2s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .sidebar-item { transition: all 0.2s ease; }
        .sidebar-item:hover { background-color: rgba(99, 102, 241, 0.1); }
        .sidebar-item.active { background-color: rgba(99, 102, 241, 0.15); border-right: 3px solid #6366f1; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex">
    <!-- Sidebar -->
    <div class="w-64 bg-white shadow-lg border-r border-gray-200 fixed h-full z-10">
        <!-- Logo Section -->
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 gradient-bg rounded-xl flex items-center justify-center">
                    <i class="fas fa-brain text-white text-lg"></i>
                </div>
                <div>
                    <h1 class="text-lg font-semibold text-gray-900">商品智算引擎</h1>
                    <span class="text-xs text-gray-500">v0.3</span>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mt-6 px-4">
            <div class="space-y-2">
                <a href="#" class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50">
                    <i class="fas fa-chart-pie w-5 h-5 mr-3 text-gray-400"></i>
                    <span class="font-medium">仪表盘</span>
                </a>
                <a href="#" class="sidebar-item active flex items-center px-4 py-3 text-indigo-600 rounded-lg">
                    <i class="fas fa-exchange-alt w-5 h-5 mr-3 text-indigo-500"></i>
                    <span class="font-medium">智能调拨</span>
                </a>
                <a href="#" class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50">
                    <i class="fas fa-chart-bar w-5 h-5 mr-3 text-gray-400"></i>
                    <span class="font-medium">数据分析</span>
                </a>
                <a href="#" class="sidebar-item flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50">
                    <i class="fas fa-cog w-5 h-5 mr-3 text-gray-400"></i>
                    <span class="font-medium">设置</span>
                </a>
            </div>
        </nav>

        <!-- User Section -->
        <div class="absolute bottom-0 w-full p-4 border-t border-gray-100">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">管理员</p>
                    <p class="text-xs text-gray-500"><EMAIL></p>
                </div>
                <button class="p-1 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 ml-64">
        <!-- Top Header -->
        <header class="bg-white shadow-sm border-b border-gray-100">
            <div class="px-6 py-4">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">智能库存调拨</h2>
                        <p class="text-gray-600 mt-1">基于AI算法的智能库存分配，解决多仓库间库存不均衡问题</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                            <i class="fas fa-bell"></i>
                        </button>
                        <button class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="p-6">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">

                <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">总池位数</p>
                            <p class="text-2xl font-bold text-gray-900">12</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center">
                            <i class="fas fa-warehouse text-blue-500"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">待调拨SKU</p>
                            <p class="text-2xl font-bold text-gray-900">1,247</p>
                        </div>
                        <div class="w-12 h-12 bg-green-50 rounded-xl flex items-center justify-center">
                            <i class="fas fa-boxes text-green-500"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">调拨效率</p>
                            <p class="text-2xl font-bold text-gray-900">94.2%</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-50 rounded-xl flex items-center justify-center">
                            <i class="fas fa-chart-line text-purple-500"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">节省成本</p>
                            <p class="text-2xl font-bold text-gray-900">¥28.5万</p>
                        </div>
                        <div class="w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center">
                            <i class="fas fa-coins text-orange-500"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Dashboard -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Algorithm Control Panel -->
                    <div class="bg-white rounded-2xl p-6 card-shadow">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">智能调拨控制台</h3>
                            <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                                <i class="fas fa-circle text-green-500 text-xs mr-1 pulse-animation"></i>
                                算法运行中
                            </span>
                        </div>
                    
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-900 mb-2">产品类型</label>
                                    <select class="w-full p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>固口类产品</option>
                                        <option>非固口类产品</option>
                                        <option>混合类型</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-900 mb-2">分配策略</label>
                                    <select class="w-full p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option>智能权重分配</option>
                                        <option>按需求比例分配</option>
                                        <option>均衡优先分配</option>
                                    </select>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-900 mb-2">主权重系数</label>
                                    <input type="range" min="0" max="100" value="75" class="w-full">
                                    <div class="flex justify-between text-sm text-gray-500 mt-1">
                                        <span>0</span>
                                        <span class="font-medium">75%</span>
                                        <span>100</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-900 mb-2">二级权重系数</label>
                                    <input type="range" min="0" max="100" value="25" class="w-full">
                                    <div class="flex justify-between text-sm text-gray-500 mt-1">
                                        <span>0</span>
                                        <span class="font-medium">25%</span>
                                        <span>100</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex space-x-4">
                            <button class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-xl font-medium hover:shadow-lg transition-all duration-300 hover-lift">
                                <i class="fas fa-play mr-2"></i>
                                开始智能调拨
                            </button>
                            <button class="px-6 py-3 border border-gray-200 text-gray-900 rounded-xl font-medium hover:bg-gray-50 transition-colors">
                                <i class="fas fa-download mr-2"></i>
                                导出方案
                            </button>
                        </div>
                    </div>

                    <!-- Data Import Section -->
                    <div class="bg-white rounded-2xl p-6 card-shadow">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">数据导入</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                                <i class="fas fa-file-excel text-green-500 text-2xl mb-2"></i>
                                <p class="text-sm font-medium text-gray-900">池位分配需求表</p>
                                <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                                <div class="mt-2">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">已上传</span>
                                </div>
                            </div>
                            <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                                <i class="fas fa-database text-blue-500 text-2xl mb-2"></i>
                                <p class="text-sm font-medium text-gray-900">共享池可用库存</p>
                                <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                                <div class="mt-2">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">已上传</span>
                                </div>
                            </div>
                            <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                                <i class="fas fa-warehouse text-purple-500 text-2xl mb-2"></i>
                                <p class="text-sm font-medium text-gray-900">目标池位在库件数</p>
                                <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                                <div class="mt-2">
                                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">待上传</span>
                                </div>
                            </div>
                        </div>

                        <!-- Data Summary -->
                        <div class="bg-gray-50 rounded-xl p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">数据概览</h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                <div>
                                    <p class="text-lg font-bold text-gray-900">1,247</p>
                                    <p class="text-xs text-gray-500">总SKU数</p>
                                </div>
                                <div>
                                    <p class="text-lg font-bold text-gray-900">12</p>
                                    <p class="text-xs text-gray-500">目标池位</p>
                                </div>
                                <div>
                                    <p class="text-lg font-bold text-gray-900">85,432</p>
                                    <p class="text-xs text-gray-500">总库存件数</p>
                                </div>
                                <div>
                                    <p class="text-lg font-bold text-gray-900">78,901</p>
                                    <p class="text-xs text-gray-500">总需求件数</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Distribution Chart -->
                    <div class="bg-white rounded-2xl p-6 card-shadow">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">库存分布分析</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium">固口类</button>
                                <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-lg text-sm">非固口类</button>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Pool Distribution -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-4">各池位库存状况</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">华东仓</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-20 bg-gray-200 rounded-full h-2">
                                                <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900">8,542</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">华南仓</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-20 bg-gray-200 rounded-full h-2">
                                                <div class="bg-blue-500 h-2 rounded-full" style="width: 72%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900">7,234</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">华北仓</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-20 bg-gray-200 rounded-full h-2">
                                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 45%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900">4,521</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">西南仓</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-20 bg-gray-200 rounded-full h-2">
                                                <div class="bg-red-500 h-2 rounded-full" style="width: 28%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900">2,801</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Category Distribution -->
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-4">产品类别分布</h4>
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">手镯类</span>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">35.2%</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">戒指类</span>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">28.7%</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">项链类</span>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">22.1%</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-4 h-4 bg-orange-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">其他类</span>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">14.0%</span>
                                    </div>
                                </div>

                                <div class="mt-6 p-4 bg-blue-50 rounded-xl">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <i class="fas fa-lightbulb text-blue-500"></i>
                                        <span class="text-sm font-medium text-gray-900">智能建议</span>
                                    </div>
                                    <p class="text-xs text-gray-600">
                                        西南仓库存严重不足，建议优先从华东仓调拨手镯类产品156件，预计可提升该区域销售效率23%
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Algorithm Status -->
                    <div class="bg-white rounded-2xl p-6 card-shadow">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">算法状态</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">数据预处理</span>
                                <span class="text-green-500">100%</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">权重计算</span>
                                <span class="text-green-500"><i class="fas fa-check-circle"></i></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">智能分配</span>
                                <span class="text-blue-500 pulse-animation"><i class="fas fa-spinner fa-spin"></i></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">结果优化</span>
                                <span class="text-gray-300"><i class="fas fa-clock"></i></span>
                            </div>
                        </div>

                        <div class="mt-6">
                            <div class="flex justify-between text-sm text-gray-600 mb-2">
                                <span>处理进度</span>
                                <span>67%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" style="width: 67%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Results -->
                    <div class="bg-white rounded-2xl p-6 card-shadow">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">最近调拨结果</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">华东仓 → 华南仓</p>
                                    <p class="text-xs text-gray-500">手镯类 · 156件</p>
                                </div>
                                <span class="text-green-500 text-sm font-medium">完成</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">华北仓 → 西南仓</p>
                                    <p class="text-xs text-gray-500">戒指类 · 89件</p>
                                </div>
                                <span class="text-blue-500 text-sm font-medium">进行中</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">华中仓 → 华东仓</p>
                                    <p class="text-xs text-gray-500">项链类 · 234件</p>
                                </div>
                                <span class="text-orange-500 text-sm font-medium">待确认</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-2xl p-6 card-shadow">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
                        <div class="space-y-3">
                            <button class="w-full text-left p-3 hover:bg-gray-50 rounded-xl transition-colors">
                                <i class="fas fa-file-download text-blue-500 mr-3"></i>
                                <span class="text-sm font-medium text-gray-900">下载调拨报告</span>
                            </button>
                            <button class="w-full text-left p-3 hover:bg-gray-50 rounded-xl transition-colors">
                                <i class="fas fa-chart-bar text-green-500 mr-3"></i>
                                <span class="text-sm font-medium text-gray-900">查看分析报表</span>
                            </button>
                            <button class="w-full text-left p-3 hover:bg-gray-50 rounded-xl transition-colors">
                                <i class="fas fa-cog text-purple-500 mr-3"></i>
                                <span class="text-sm font-medium text-gray-900">算法参数设置</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Algorithm Details Modal (Hidden by default) -->
    <div id="algorithmModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-2xl p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-900">智能调拨算法详情</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-900">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Algorithm Flow -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">算法流程</h4>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                            <div>
                                <h5 class="font-medium text-gray-900">产品分类识别</h5>
                                <p class="text-sm text-gray-600">自动识别固口类与非固口类产品，应用不同处理策略</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                            <div>
                                <h5 class="font-medium text-gray-900">库存状况评估</h5>
                                <p class="text-sm text-gray-600">分析总库存与总需求，判断充足或不足情况</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                            <div>
                                <h5 class="font-medium text-gray-900">智能权重计算</h5>
                                <p class="text-sm text-gray-600">主权重优先库存少的池位，二级权重平衡SKU分配</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                            <div>
                                <h5 class="font-medium text-gray-900">随机化分配</h5>
                                <p class="text-sm text-gray-600">防止SKU偏向性操控，确保分配公平性</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                            <div>
                                <h5 class="font-medium text-gray-900">结果优化输出</h5>
                                <p class="text-sm text-gray-600">生成各池位独立分配文件，支持Excel导出</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Algorithm Parameters -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">核心参数</h4>
                    <div class="space-y-4">
                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-gray-900 mb-2">主权重系数 (α)</h5>
                            <p class="text-sm text-gray-600 mb-3">控制库存均衡的优先级，值越大越倾向于给库存少的池位分配更多商品</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.5</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="text-sm text-gray-500">1.0</span>
                                <span class="text-sm font-medium text-gray-900">0.75</span>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-gray-900 mb-2">二级权重系数 (β)</h5>
                            <p class="text-sm text-gray-600 mb-3">平衡SKU分配效率，确保库存多的SKU能够优先分配</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.1</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 50%"></div>
                                </div>
                                <span class="text-sm text-gray-500">0.5</span>
                                <span class="text-sm font-medium text-gray-900">0.25</span>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-gray-900 mb-2">随机化因子 (γ)</h5>
                            <p class="text-sm text-gray-600 mb-3">防止算法偏向性，增加分配结果的随机性和公平性</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.0</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-purple-500 h-2 rounded-full" style="width: 30%"></div>
                                </div>
                                <span class="text-sm text-gray-500">0.3</span>
                                <span class="text-sm font-medium text-gray-900">0.1</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h5 class="font-medium text-gray-900 mb-3">算法优势</h5>
                        <div class="space-y-2">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">随机性防止SKU偏向操控</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">可控性确保不超总库存</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">灵活输出支持多格式导出</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">参数可调适应不同业务</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end space-x-4">
                <button onclick="closeModal()" class="px-6 py-2 border border-gray-200 text-gray-900 rounded-xl hover:bg-gray-50">
                    关闭
                </button>
                <button class="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:shadow-lg">
                    应用设置
                </button>
            </div>
        </div>
    </div>

    <script>
        function openModal() {
            document.getElementById('algorithmModal').classList.remove('hidden');
            document.getElementById('algorithmModal').classList.add('flex');
        }

        function closeModal() {
            document.getElementById('algorithmModal').classList.add('hidden');
            document.getElementById('algorithmModal').classList.remove('flex');
        }

        // Add click event to algorithm details button
        document.addEventListener('DOMContentLoaded', function() {
            // Add modal trigger to algorithm settings button
            const settingsButtons = document.querySelectorAll('button');
            settingsButtons.forEach(button => {
                if (button.textContent.includes('算法参数设置')) {
                    button.addEventListener('click', openModal);
                }
            });

            // Sidebar navigation
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            sidebarItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all items
                    sidebarItems.forEach(i => i.classList.remove('active'));

                    // Add active class to clicked item
                    this.classList.add('active');

                    // Update page title based on clicked item
                    const pageTitle = this.querySelector('span').textContent;
                    document.querySelector('h2').textContent = pageTitle;

                    // Update page description
                    const descriptions = {
                        '仪表盘': '全面监控系统运行状态和关键指标',
                        '智能调拨': '基于AI算法的智能库存分配，解决多仓库间库存不均衡问题',
                        '数据分析': '深度分析库存数据，提供决策支持和趋势预测',
                        '设置': '系统配置和参数设置'
                    };

                    const description = descriptions[pageTitle] || '系统功能模块';
                    document.querySelector('h2').nextElementSibling.textContent = description;
                });
            });

            // Simulate real-time updates
            setInterval(() => {
                const progressBar = document.querySelector('.bg-gradient-to-r.from-blue-500.to-purple-600');
                if (progressBar) {
                    const currentWidth = parseInt(progressBar.style.width) || 67;
                    const newWidth = Math.min(currentWidth + Math.random() * 2, 100);
                    progressBar.style.width = newWidth + '%';

                    const progressText = document.querySelector('.text-sm.text-gray-600 + span');
                    if (progressText) {
                        progressText.textContent = Math.round(newWidth) + '%';
                    }
                }
            }, 3000);
        });
    </script>


</body></html>